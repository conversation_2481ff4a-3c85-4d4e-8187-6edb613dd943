import 'dart:developer';

import 'package:boutigak/controllers/badge_controller.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/user.dart';
import 'package:boutigak/data/services/auth_service.dart';
import 'package:boutigak/data/services/session_service.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:firebase_messaging/firebase_messaging.dart';

class AuthController extends GetxController {
  var isAuthenticated = false.obs;
  String? fcmToken;
  RxString token ="".obs;
  User? user;
  RxBool hasSubscription = false.obs;

  var tokenLoading = true.obs;
  
  var isLoading = false.obs;


  final BadgeController badgeController = Get.put(BadgeController());

  
  void setUser(User? value){
    
    user = value;
    hasSubscription.value = user?.hasSubscription ?? false;
    update();
  }






   getAndSaveUser() async {

    print('getAndSaveUser called');
    final response = await AuthService.getUser();
    print('getAndSaveUser response: ${response}');
    if (response != null) {
      User user = User.fromJson(response['user']);
      setUser(user);
    }
  }

@override
void onInit() {
  super.onInit();

  // Récupérer le token et vérifier l'authentification
  SessionService.getToken().then((value) async {
    tokenLoading.value = false;
    
    _getFCMToken();
    setToken(value);

    // If authenticated, fetch user information
    if (isAuthenticated.value) {
      try {

        


       badgeController.fetchBadgeCounts();


        try {
          final itemController = Get.find<ItemController>();
          await itemController.fetchItems(refresh: true);
          print('Items fetched successfully from AuthController');
        } catch (e) {
          print('Error fetching items from AuthController: $e');
        }

      

        await getAndSaveUser();
      } catch (e) {
        print('Error fetching user information: $e');
      }
    }
  });

  // Récupérer l'utilisateur enregistré
  SessionService.getLoggedUser().then((value) {
    setUser(value);
  });
}

// Fonction pour récupérer le token FCM
  void _getFCMToken() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;


     try {
    fcmToken = await messaging.getToken();
  //  print("FCM Token: $fcmToken");

  }
  catch (e) {
  //  print('Error getting FCM token: $e');
  }
 
    // Si l'utilisateur est déjà authentifié, envoyer le token immédiatement



 //   print('vefire check');
    if (isAuthenticated.value && fcmToken != null   ) {


  //     print('is authenticated');

      _sendTokenToAPI(fcmToken!);
    }
  }

Future<void> deleteAccount(String password, String deleteReason) async {
  try {
    isLoading.value = true;

    // Appel service
    final success = await AuthService.deleteAccount(
      password: password,
      deleteReason: deleteReason,
    );


    log('sucess ${success}');


    if (success) {

      
      // Si succès => On déconnecte (puisqu'il n'y a plus de compte)
      await logout();
      // Optionnel: message de confirmation
      Get.snackbar("Succès", "Votre compte sera supprimé sous 30 jours");
      // Redirection ou autre
    } else {
      // En cas d'échec, on ne fait rien de spécial
      // (un snackbar d'erreur peut déjà être géré dans le service, ou ici)
    }
  } finally {
    isLoading.value = false;
  }
}

  // Fonction pour envoyer le token FCM à l'API via AuthService
  void _sendTokenToAPI(String tokenFCM) async {


    log('send token to api ${tokenFCM}');
    var success = await AuthService.sendTokenToAPI(tokenFCM);
    if (success) {
    //  print("FCM Token envoyé avec succès à l'API");
    } else {
      print("Erreur lors de l'envoi du FCM Token à l'API");
    }
  }

Future<bool> changeLanguage(String lang) async {
  try {
    final response = await AuthService.updateLanguage(lang: lang);

    if (response != null && response['message'] == "Language updated successfully") {
    
      return true;
    } else {
    
      return false;
    }
  } catch (e) {
 
    return false;
  }
}



  void setToken(String value){


    

    print('token ... ${token}');


    token.value=value;



    if (value.isNotEmpty) {
      isAuthenticated.value = true;
    } else {
      isAuthenticated.value = false;
    }

    update();
  }


  bool checkTokenExpiration(String token) {
    return JwtDecoder.isExpired(token);
  }
Future<void> logout() async {
  isLoading.value = true;
  await AuthService.logout();                   
  await SessionService.logout();               
  setToken("");
  user = null;                             
  update();                                 
  isLoading.value = false;

  Get.offAllNamed('/');  
}

}

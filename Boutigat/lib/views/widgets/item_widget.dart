import 'dart:developer';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/matterport_webview_page.dart';
import 'package:boutigak/views/widgets/snapchat_item_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import '/constants/app_colors.dart';
import 'image_slider_widget.dart';
import 'dart:ui';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:shimmer/shimmer.dart';
class ItemWidget extends StatelessWidget {
  final Item item;
  final InboxController inboxController = Get.put(InboxController());

  ItemWidget({super.key, required this.item});

  @override
  Widget build(BuildContext context) {


    log('item details ${item.categoryItemDetails.length}');
    double screenWidth = MediaQuery.of(context).size.width;
  
 
    return SizedBox(
      width: screenWidth * 0.4388,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              // Only open the modal, do NOT update any observable or call selectItem here
              _showModalBottomSheet(context, item);
            },
            child: Stack(
              children: [
                CachedImageWidget(
                  imageUrl: item.images.isNotEmpty
                      ? '${item.images.first}'
                      : '',
                  width: screenWidth * 0.4388,
                  height: screenWidth * 0.5485,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(8),
                ),
                if (item.isPromoted ?? false)
                  Positioned(
                    top: 10,
                    left: 10,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.all(
                          Radius.circular(5),
                        
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: EdgeInsets.all(1),
                            decoration: const BoxDecoration(
                              color: Colors.yellow,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(Icons.monetization_on,
                                color: Theme.of(context).colorScheme.onSurface, size: 15),
                          ),
                          SizedBox(width: 4),
                          Text(
                            'promoted'.tr,
                            style: TextStyle(
                                color: Theme.of(context).colorScheme.onSurface,
                                fontSize: 10,
                                fontWeight: AppFontWeights.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(height: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.getTitle(),
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: AppColors.onSurface,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Row(
  mainAxisAlignment: MainAxisAlignment.end,
  children: [
    Text(
      '${item.price.toStringAsFixed(0)} ',
      style: const TextStyle(
        fontSize: 13,
        fontWeight: AppFontWeights.extraBold,
      ),
    ),
    Text(
      'mru'.tr,
      style: const TextStyle(
        fontSize: 13,
        fontWeight: AppFontWeights.extraBold,
        color: AppColors.primary,
      ),
    ),
  ],
)
            ],
          )
        ],
      ),
    );
  }

void _showModalBottomSheet(BuildContext context, Item item) {
  PageController pageController = PageController();
  int currentPage = 0;
  bool isFavorited = item.isLiked;

  void showPaymentBottomSheet(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.8,
            child: CapturableItemWidget(
              imageUrl: '${item.images.first}',
              title: item.getTitle(),
              brand: item.brandName,
              ownerName: item.userName,
              price: item.price,
              itemId: item.id!,
            ),
          );
        },
      );
    });
  }

  ConversationController conversationController = Get.put(ConversationController());
  ItemController itemController = Get.find<ItemController>();
  final AuthController authController = Get.put(AuthController());

  // Do NOT call itemController.selectItem or any observable update here
  showModalBottomSheet(
    backgroundColor: Theme.of(context).colorScheme.surface,
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (BuildContext modalContext) {
      return ItemDetailModal(
        item: item,
        pageController: pageController,
        currentPage: currentPage,
        isFavorited: isFavorited,
        showPaymentBottomSheet: showPaymentBottomSheet,
        itemController: itemController,
        conversationController: conversationController,
        authController: authController,
      );
    },
  );
}}
class ItemDetailModal extends StatefulWidget {
  final Item item;
  final PageController pageController;
  final int currentPage;
  final bool isFavorited;
  final Function showPaymentBottomSheet;
  final ItemController itemController;
  final ConversationController conversationController;
  final AuthController authController;

  const ItemDetailModal({
    Key? key,
    required this.item,
    required this.pageController,
    required this.currentPage,
    required this.isFavorited,
    required this.showPaymentBottomSheet,
    required this.itemController,
    required this.conversationController,
    required this.authController,
  }) : super(key: key);

  @override
  _ItemDetailModalState createState() => _ItemDetailModalState();
}

class _ItemDetailModalState extends State<ItemDetailModal> {
  late int currentPage;
  late bool isFavorited;

  @override
  void initState() {
    super.initState();
    currentPage = widget.currentPage;
    isFavorited = widget.isFavorited;
    // Fetch item details and update observables in controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.itemController.fetchItemById(widget.item.id!);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isLoading = widget.itemController.isLoading.value;
      final errorMessage = widget.itemController.errorMessage.value;
      final updatedItem = widget.itemController.selectedItem.value;

      if (isLoading) {
  return SizedBox(
    height: double.infinity,
    width: double.infinity,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // ✅ Image sans padding
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: double.infinity,
            height: MediaQuery.of(context).size.height * .47,
            color: Colors.grey[300],
          ),
        ),

        // ✅ Corps scrollable
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Titre + Coeur
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 200,
                        height: 22,
                        color: Colors.grey[300],
                      ),
                    ),
                    const Spacer(),
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 30,
                        height: 30,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // By user
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    width: 140,
                    height: 18,
                    color: Colors.grey[300],
                  ),
                ),
                const SizedBox(height: 8),

                // Date
                Align(
                  alignment: Alignment.centerRight,
                  child: Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      width: 80,
                      height: 14,
                      color: Colors.grey[300],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Description title
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    width: 120,
                    height: 20,
                    color: Colors.grey[300],
                  ),
                ),
                const SizedBox(height: 8),

                // Description
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    width: double.infinity,
                    height: 60,
                    color: Colors.grey[300],
                  ),
                ),
                const SizedBox(height: 16),

                // Table détails
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.grey.shade300, width: 1),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.08),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // En-tête
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(10),
                            topRight: Radius.circular(10),
                          ),
                        ),
                        child: Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: 80,
                            height: 16,
                            color: Colors.grey[300],
                          ),
                        ),
                      ),
                      Divider(height: 1, color: Colors.grey.shade300, thickness: 1),

                      // Lignes (5 lignes)
                      ...List.generate(5, (i) => Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 120,
                                height: 40,
                                color: Colors.grey.shade100,
                                alignment: Alignment.centerLeft,
                                padding: const EdgeInsets.symmetric(horizontal: 12),
                                child: Shimmer.fromColors(
                                  baseColor: Colors.grey[300]!,
                                  highlightColor: Colors.grey[100]!,
                                  child: Container(
                                    width: 60,
                                    height: 12,
                                    color: Colors.grey[300],
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 12),
                                  child: Shimmer.fromColors(
                                    baseColor: Colors.grey[300]!,
                                    highlightColor: Colors.grey[100]!,
                                    child: Container(
                                      height: 12,
                                      color: Colors.grey[200],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (i < 4)
                            Divider(height: 1, color: Colors.grey.shade300, thickness: 1),
                        ],
                      )),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        // ✅ Shimmer prix + bouton en bas
        Padding(
          padding: const EdgeInsets.only(bottom: 30.0, left: 25, right: 25, top: 10),
          child: Row(
            children: [
              // Prix
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      width: 60,
                      height: 12,
                      color: Colors.grey[300],
                    ),
                  ),
                  const SizedBox(height: 6),
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      width: 80,
                      height: 18,
                      color: Colors.grey[300],
                    ),
                  ),
                ],
              ),
              const Spacer(),
              // Bouton "make an offer"
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 200,
                  height: 50,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: Colors.grey[300],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}


      if (errorMessage.isNotEmpty) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.6,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 48,
                  color: Colors.red,
                ),
                SizedBox(height: 16),
                Text(
                  'Failed to load item details',
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => widget.itemController.fetchItemById(widget.item.id!),
                  child: Text('Retry'),
                ),
              ],
            ),
          ),
        );
      }

      if (updatedItem == null) {
        return SizedBox(
          width: double.infinity,
          height: MediaQuery.of(context).size.height,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CupertinoActivityIndicator(radius: 20),
                SizedBox(height: 16),
                Text('Loading...'),
              ],
            ),
          ),
        );
      }

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Container(
                width: double.infinity,
                height: MediaQuery.of(context).size.height * .47,
                child: ImageSlider(
                  pageController: widget.pageController,
                  photos: updatedItem.images.map((image) => '$image').toList(),
                  currentPage: currentPage,
                  onPageChanged: (int page) => setState(() => currentPage = page),
                  borderRadius: 0,
                ),
              ),
              Positioned(
                top: 60,
                right: 10,
                child: ClipOval(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                    child: Container(
                      color: AppColors.onBackground.withOpacity(0.2),
                      child: IconButton(
                        iconSize: 20,
                        icon: Icon(FontAwesomeIcons.upRightFromSquare, color: AppColors.background),
                        onPressed: () {
                          widget.showPaymentBottomSheet(context);
                        },
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 60,
                left: 10,
                child: ClipOval(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                    child: Container(
                      color: AppColors.onBackground.withOpacity(0.3),
                      child: IconButton(
                        iconSize: 20,
                        icon: Icon(FontAwesomeIcons.chevronDown, color: AppColors.background),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ),
                  ),
                ),
              ),
              if (updatedItem.matterportLink != null && updatedItem.matterportLink!.isNotEmpty)
                Positioned(
                  bottom: 40,
                  right: 10,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => MatterportView(
                              matterportLink: updatedItem.matterportLink!,
                            ),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(20),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(FontAwesomeIcons.cube, color: AppColors.background, size: 24),
                          const SizedBox(width: 6),
                          Text('3d_view'.tr, style: TextStyle(color: AppColors.background, fontSize: 14, fontWeight: FontWeight.bold)),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
          
          // Container(
          //   width: double.infinity,
          //   height: 32,
          //   decoration: const BoxDecoration(
          //     color: Colors.white,
          //     borderRadius: BorderRadius.only(
          //       topLeft: Radius.circular(32),
          //       topRight: Radius.circular(32),
          //     ),
          //     boxShadow: [
          //       BoxShadow(
          //         color: Colors.black12,
          //         blurRadius: 8,
          //         offset: Offset(0, -2),
          //       ),
          //     ],
          //   ),
          // ),
          Expanded(
            child: Container(
              width: double.infinity,
              color: Colors.transparent,
              child: SingleChildScrollView(
                padding: const EdgeInsets.only(top: 0, left: 0, right: 0, bottom: 0),
                child: InfoSection(
                  item: updatedItem,
                  isFavorited: isFavorited,
                  toggleFavorite: () => setState(() => isFavorited = !isFavorited),
                ),
              ),
            ),
          ),
          // Bottom action bar always visible
          Padding(
            padding: const EdgeInsets.only(bottom: 30.0, left: 25, right: 25, top: 10),
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "price".tr + "(" + "mru".tr + ")",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).disabledColor,
                      ),
                    ),
                    Text(
                      "${updatedItem.price.toStringAsFixed(0)} ",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Container(
                  width: 200,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.onSurface,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 5,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: TextButton(
                    onPressed: () async {
                      if (widget.authController.isAuthenticated.value) {
                        dynamic success = await widget.conversationController.createDiscussion(updatedItem.id!);
                        if (success != null) {
                          Get.to(() => ConversationPage(
                                itemId: updatedItem.id!,
                                item: updatedItem.toJson(),
                                discussionId: success['discussion']['id'],
                                interlocutor: updatedItem.userName!,
                                phoneNumber: success['discussion']['interlocutor_phone'] ?? null, // Phone number will be fetched from discussion data
                              ));
                        }
                      } else {
                        Get.to(() => LoginPage());
                      }
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        "make_an_offer".tr,
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}

class InfoSection extends StatefulWidget {
  final Item item; // Keep this to receive updated data from parent
  final bool isFavorited;
  final void Function()? toggleFavorite;

  InfoSection({
    required this.item,
    required this.isFavorited,
    required this.toggleFavorite,
  });

  @override
  _InfoSectionState createState() => _InfoSectionState();
}

class _InfoSectionState extends State<InfoSection> {
  late bool isFavorited;

  @override
  void initState() {
    super.initState();
    isFavorited = widget.item.isLiked;
  }

  @override
  void didUpdateWidget(covariant InfoSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.item != oldWidget.item) {
      isFavorited = widget.item.isLiked;
    }
  }

  Future<void> _toggleFavorite() async {
    bool success = await ItemService.likeUnlikeItem(widget.item.id!, isFavorited);
    if (success) {
      setState(() {
        isFavorited = !isFavorited;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
        ),
        child: ListView(
          shrinkWrap: true,
          primary: false,
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(widget.item.getTitle(),
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    Row(
                      children: [
                        Text(
                          "by".tr,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          " ${widget.item.userName}",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).disabledColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Spacer(),
                IconButton(
                  icon: Icon(
                    isFavorited ? Icons.favorite : Icons.favorite_border,
                    size: 30,
                    color: Colors.red,
                  ),
                  onPressed: _toggleFavorite,
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
              child: Row( mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    widget.item.createdAt != null 
                        ? "${timeago.format(widget.item.createdAt!)} " 
                        : "Date unknown",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 6.0),
              child: Text(
                "Description",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: AppFontWeights.bold,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 6.0),
              child: Text(
                widget.item.getDescription(),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: AppFontWeights.medium,
                ),
              ),
            ),
            // Details Section styled as a bordered table like the image
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.grey.shade300, width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.08),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    child: Text(
                      "details".tr,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Divider(height: 1, color: Colors.grey.shade300, thickness: 1),
                  _buildTableRow("condition".tr, widget.item.condition, isFirst: true),
                  Divider(height: 1, color: Colors.grey.shade300, thickness: 1),
                  _buildTableRow("category".tr, widget.item.categoryName ?? "Unknown"),
                  Divider(height: 1, color: Colors.grey.shade300, thickness: 1),
                  _buildTableRow("brand".tr, widget.item.brandName ?? "Unknown", isLast: widget.item.categoryItemDetails == null || widget.item.categoryItemDetails.isEmpty),

                  // build catgroy category_item_details 
                  if (widget.item.categoryItemDetails != null && widget.item.categoryItemDetails.isNotEmpty)
                    ...widget.item.categoryItemDetails.map<Widget>((detail) {
                      // Pick label based on locale, fallback to English
                      String label = detail.labelEn;
                      if (Get.locale?.languageCode == 'ar') {
                        label = detail.labelAr ?? label;
                      } else if (Get.locale?.languageCode == 'fr') {
                        label = detail.labelFr ?? label;
                      }
                      String value = detail.value?.toString() ?? '';
                      return Column(
                        children: [
                          Divider(height: 1, color: Colors.grey.shade300, thickness: 1),
                          _buildTableRow(label, value, isLast: false),
                        ],
                      );
                    }).toList(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

 Widget _buildTableRow(String label, String value, {bool isFirst = false, bool isLast = false}) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.only(
        topLeft: isFirst ? const Radius.circular(10) : Radius.zero,
        topRight: isFirst ? const Radius.circular(10) : Radius.zero,
        bottomLeft: isLast ? const Radius.circular(10) : Radius.zero,
        bottomRight: isLast ? const Radius.circular(10) : Radius.zero,
      ),
    ),
    child: Row(
      children: [
        // ✅ Colonne de gauche avec fond gris
        Container(
          width: 120,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          color: Colors.grey.shade100,
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.black87,
              fontSize: 14,
            ),
          ),
        ),
        // ✅ Colonne de droite
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.black54,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

}

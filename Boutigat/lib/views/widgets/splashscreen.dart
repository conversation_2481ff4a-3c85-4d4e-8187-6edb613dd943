import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/badge_controller.dart';
import 'package:boutigak/views/profil/address_management_page.dart';
import 'package:boutigak/views/profil/boutigak/boutigak_user_page.dart';
import 'package:boutigak/views/profil/delete_account_page.dart';
import 'package:boutigak/views/profil/my_information_page.dart';
import 'package:boutigak/views/profil/my_items.dart';
import 'package:boutigak/views/profil/my_order_page.dart';
import 'package:boutigak/views/profil/password_page.dart';
import 'package:boutigak/views/profil/settings_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:lottie/lottie.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
 // Remplacez par votre page principale

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _navigateToHome();
  }

  void _navigateToHome() async {
    await Future.delayed(Duration(seconds: 3), () {
      Get.offAll(
  () => ZoomDrawerWrapper(),
  transition: Transition.noTransition,
);
});}

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Lottie.asset(
          'assets/lottie/Boutigaklogo.json',
          width: 400.w,
          height: 400.h,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}



class ZoomDrawerWrapper extends StatelessWidget {
  final bool shouldOpenDrawer;

  ZoomDrawerWrapper({this.shouldOpenDrawer = false});

  final _drawerController = ZoomDrawerController();

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: Size(375, 812));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (shouldOpenDrawer) {
        _drawerController.open!(); // Open after widget is built
      }
    });

    return ZoomDrawer(
      controller: _drawerController,
      menuScreen: MenuScreen(controller: _drawerController),
      mainScreen: NavigationBarPage(),
      borderRadius: 24.r,
      showShadow: true,
      angle: 0,
      slideWidth: MediaQuery.of(context).size.width * 0.75,
      menuBackgroundColor: Theme.of(context).colorScheme.primary,
      openCurve: Curves.easeOut,
      closeCurve: Curves.easeIn,
    );
  }
}

class MenuScreen extends StatelessWidget {
  final ZoomDrawerController controller;
  MenuScreen({required this.controller});

  final authController = Get.find<AuthController>();
  final badgeController = Get.find<BadgeController>();

  Widget customTile(String title, IconData icon, Widget page, {int badgeCount = 0}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white, width: 1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8.r),
          onTap: () {
            controller.close!();
            Future.delayed(Duration(milliseconds: 250), () {
              Get.to(() => page);
            });
          },
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            child: Row(
              children: [
                // Icon with badge overlay
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    FaIcon(icon, size: 20.sp, color: Colors.white),
                    if (badgeCount > 0)
                      Positioned(
                        right: -6.w,
                        top: -6.h,
                        child: Container(
                          constraints: BoxConstraints(minWidth: 16.w),
                          height: 16.h,
                          padding: EdgeInsets.symmetric(horizontal: 4.w),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(color: Colors.white, width: 1.5),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 3,
                                offset: Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Text(
                              badgeCount > 99 ? '99+' : '$badgeCount',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 9.sp,
                                height: 1.0,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(fontSize: 12.sp, color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
Widget buildSocialTilesRow() {
  final List<Map<String, dynamic>> socials = [
    {
      'icon': FontAwesomeIcons.whatsapp,
      
      'url': 'https://wa.me/221234567890', // Numéro WhatsApp avec indicatif
    },
    {
      'icon': FontAwesomeIcons.facebookF,
 
      'url': 'https://www.facebook.com/TonNomDePage',
    },
    {
      'icon': FontAwesomeIcons.snapchatGhost,
     
      'url': 'https://www.snapchat.com/add/tonpseudo',
    },
    {
      'icon': FontAwesomeIcons.tiktok,
      
      'url': 'https://www.tiktok.com/@tonpseudo',
    },
  ];

  return Padding(
    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: socials.map((data) {
        return Expanded(
          child: Container(
            height: 45.w,
            margin: EdgeInsets.symmetric(horizontal: 4.w),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.white, width: 1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(8.r),
                onTap: () async {
                  final uri = Uri.parse(data['url']);
                  if (await canLaunchUrl(uri)) {
                    await launchUrl(uri, mode: LaunchMode.externalApplication);
                  } else {
                    Get.snackbar('Erreur', 'Impossible d’ouvrir le lien');
                  }
                },
                child: Center(
                  child: FaIcon(
                    data['icon'],
                    size: 24.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        );
      }).toList(),
    ),
  );
}

  @override
  Widget build(BuildContext context) {
    if (authController.user == null) {
      return Scaffold(
        backgroundColor: Theme.of(context).colorScheme.primary,
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 20.h, ),
            child: Column(
              children: [
                 Padding(
                padding: EdgeInsets.only(top: 16.h, bottom: 180.h, left: 16.w,right: 16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
        
                         Container(
          width: 36.w,
          height: 36.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white.withOpacity(0.15),
          ),
          child: IconButton(
            padding: EdgeInsets.zero, // ✅ Supprime le padding
            icon: Icon(Icons.close, color: Colors.white, size: 24.sp),
            onPressed: () => controller.close?.call(),
            splashRadius: 24.r,
          ),
        ),
        
                        Text(
                          '👋 ' + "hello".tr,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 32.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                       
                      ],
                    ),
               
                  ],
                ),
              ),
                  Padding(
         padding: EdgeInsets.symmetric(vertical:16),
         child: SizedBox(
          width: 150,
          
          child: ColorFiltered(
            colorFilter: const ColorFilter.mode(
              AppColors.surface,
              BlendMode.srcATop,
            ),
            child: Image.asset(
             'assets/images/biglogo_boutigak.png',
              fit: BoxFit.contain,
            ),
          ),
               ),
       ),
                 Padding(
                      padding: EdgeInsets.only(top: 20.h,bottom: 169.h),
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 16.w),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white, width: 1),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(8.r),
                            onTap: ()  {
                    controller.close!();
                    Future.delayed(Duration(milliseconds: 250), () {
                      Get.to(() => LoginPage());
                    });
                  },
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 12.h, horizontal: 16.w),
                              child: Row(
                                children: [
                                  FaIcon(
                                    FontAwesomeIcons.rightToBracket,
                                    size: 20.sp,
                                    color: Color(0xFFF4AE4E),
                                  ),
                                  SizedBox(width: 16.w),
                                  Expanded(
                                    child: Text(
                                      'login'.tr,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: Colors.white,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
              
                buildSocialTilesRow(),
                                Padding(
                      padding:
                          EdgeInsets.only(left: 16.w, right: 16.w, top: 15.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Spacer(),
                          Text(
                            'powered_by'.tr,
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400,
                              color:
                                  Theme.of(context).colorScheme.surface,
                            ),
                          ),
                          Text(
                            'BÉCOD',
                            style: TextStyle(
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w900,
                              color:
                                  Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          Spacer(),
                          
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 8.h),
                      child: Center(
                        child: Text(
                          'Version 1.0.0',
                          style: TextStyle(
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w300,
                            color:
                                Theme.of(context).colorScheme.surface,
                          ),
                        ),
                      ),
                    ),
              ],
            ),
          ),
        ),
      );
    }

    // Si l'utilisateur est connecté
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.primary,
      body: Obx(
        () => SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
        
                         Container(
          width: 36.w,
          height: 36.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white.withOpacity(0.15),
          ),
          child: IconButton(
            padding: EdgeInsets.zero, // ✅ Supprime le padding
            icon: Icon(Icons.close, color: Colors.white, size: 24.sp),
            onPressed: () => controller.close?.call(),
            splashRadius: 24.r,
          ),
        ),
        
                        Text(
                          '👋 ' + "hello".tr,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 32.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                       
                      ],
                    ),
                    Text(
                      '${authController.user?.firstName}!',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView(
                  padding: EdgeInsets.only(top: 30.h),
                  children: [
                    customTile(
                      'my_orders'.tr,
                      FontAwesomeIcons.box,
                      MyOrdersPage(),
                      badgeCount: badgeController.getModuleCount('user-order'),
                    ),
                    customTile(
                      'my_items'.tr,
                      FontAwesomeIcons.scroll,
                      MyItemsPage(),
                      badgeCount: badgeController.getModuleCount('items'),
                    ),
                    customTile(
                      'boutigak'.tr,
                      FontAwesomeIcons.store,
                      BoutigakUserPage(),
                      badgeCount: badgeController.getModuleCount('store-order'),
                    ),
                    customTile(
                      'settings'.tr,
                      FontAwesomeIcons.gear,
                      SettingsPage(),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 50.h),
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 16.w),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white, width: 1),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(8.r),
                            onTap: () => authController.logout(),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 12.h, horizontal: 16.w),
                              child: Row(
                                children: [
                                  FaIcon(
                                    FontAwesomeIcons.rightFromBracket,
                                    size: 20.sp,
                                    color: Color(0xFFF4AE4E),
                                  ),
                                  SizedBox(width: 16.w),
                                  Expanded(
                                    child: Text(
                                      'log_out'.tr,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: Colors.white,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  
                  ],
                ),
              ),
              buildSocialTilesRow(),
                Padding(
                      padding:
                          EdgeInsets.only(left: 16.w, right: 16.w, top: 15.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Spacer(),
                          Text(
                            'powered_by'.tr,
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400,
                              color:
                                  Theme.of(context).colorScheme.surface,
                            ),
                          ),
                          Text(
                            'BÉCOD',
                            style: TextStyle(
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w900,
                              color:
                                  Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          Spacer(),
                          
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 8.h),
                      child: Center(
                        child: Text(
                          'Version 1.0.0',
                          style: TextStyle(
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w300,
                            color:
                                Theme.of(context).colorScheme.surface,
                          ),
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

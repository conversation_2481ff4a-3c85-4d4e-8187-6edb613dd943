import 'dart:ui';

import 'package:boutigak/views/profil/address_management_page.dart';
import 'package:boutigak/views/profil/delete_account_page.dart';
import 'package:boutigak/views/profil/my_items.dart';
import 'package:boutigak/views/profil/settings_page.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:get/get.dart';

import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import 'boutigak/boutigak_user_page.dart';
import 'password_page.dart';

import 'my_information_page.dart';
import 'my_order_page.dart';

import 'privacy_police_page.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:boutigak/controllers/badge_controller.dart';

class ProfilPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
 final BadgeController badgeController = Get.find<BadgeController>();
    final authController = Get.find<AuthController>();
    // final UserController userController = Get.find<UserController>();
    // final User user = userController.users.first;
     ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      
    );
        void openWhatsApp(String phoneNumber) async {
  final Uri url = Uri.parse('https://wa.me/$phoneNumber');

  if (await canLaunchUrl(url)) {
    await launchUrl(url);
  } else {
    throw 'Could not launch $url';
  }
}
Widget customListTile(
    String title,
    IconData icon,
    Widget page,
  ) {
    return InkWell(
      onTap: () => Get.to(() => page),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Icon(icon, size: 20.w),
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Text(title, style: TextStyle(fontSize: 14.sp,),),
              ),
            ),
             Icon(
              Icons.arrow_forward_ios,
              size: 16.sp,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }

  Widget customListTileWithBadge(String title, IconData icon, Widget page, int badgeCount) {
    return InkWell(
      onTap: () {
        Get.to(() => page);
      },
      child: Container(
         padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
       
        child: Row(
          children: [
            FaIcon(icon,size: 20.w ),
            SizedBox(width: 15.w),
            Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                
              ),
            ),
            Spacer(),
            if (badgeCount > 0)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                constraints: BoxConstraints(
                  minWidth: 20,
                  minHeight: 20,
                ),
                child: Text(
                  badgeCount > 99 ? '99+' : badgeCount.toString(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            SizedBox(width: 5.w),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.sp,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }
 

    // Redirect to login if user is null
    if (authController.user == null) {
      Future.microtask(() => Get.toNamed('/login'));
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.primary,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        leading: IconButton(
          icon: const Icon(Icons.close),
          color: Theme.of(context).colorScheme.surface,
          onPressed: () {
 
    Get.back(); // Ferme la route actuelle

},

        ),
        actions: [
       
        ],
        ),
      body: SingleChildScrollView (
        child: Column(
          children: <Widget>[
            Container(
              color: AppColors.primary,
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left :16.h,
                      bottom :16.h ,
                    ),
                    child: Align(
                      alignment: Alignment.topLeft,
                      child:Text("Hello, ${authController.user?.firstName ?? ''}!",style: TextStyle(
                              fontSize: 24.sp,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.surface,)
                              ),
                              ),
        
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(15.r),
                        topRight: Radius.circular(15.r),
                      ),
                    ),
                    child: Column(
                      children: [
                        Padding(
                    padding: EdgeInsets.only(
                      left :16.w,
                      bottom :4.h ,top:12.h ,
                    ),
                    child: Align(
                      alignment: Alignment.topLeft,
                      child: Text("Account",
                          style: TextStyle(
                              fontSize: 24.sp,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,)),
                    ),
                  ),
                 
                  InkWell(
      onTap: () => Get.to(() => MyInformationPage(firstName: '${authController.user?.firstName}', lastName: '${authController.user?.lastName}',)),
      child: Padding(
        padding: EdgeInsets.only(
          left: 16.w,
         
          top: 15.h,
          bottom: 4.h,
        ),
        child: Row(
          
        
          children: [
            Icon(FontAwesomeIcons.solidUser, size: 20.w),
            SizedBox(width: 16.w),
            Container(
              width: 135.w, 
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                '${authController.user?.firstName}',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(width: 10.w),
            Container(
              width: 135.w, 
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                '${authController.user?.lastName}',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(width: 10.w),
           Icon(
              Icons.arrow_forward_ios,
              size: 16.sp,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    ),
                   SizedBox(height: 15.h),
        Obx(() => customListTileWithBadge(
          'my_orders'.tr,
          FontAwesomeIcons.box,
          MyOrdersPage(),
          badgeController.getModuleCount('user-order'),
        )),
        SizedBox(height: 15.h),
        Obx(() => customListTileWithBadge(
          'my_items'.tr,
          FontAwesomeIcons.scroll,
          MyItemsPage(),
          badgeController.getModuleCount('items'),
        )),
        SizedBox(height: 15.h),
        customListTile(
          'My Addresses',
          FontAwesomeIcons.locationDot,
          AddressManagementPage(),
        ),
        SizedBox(height: 15.h),
        customListTile(
          'Boutigak ',
          FontAwesomeIcons.store,
          BoutigakUserPage(),
        ),
        SizedBox(height: 15.h),
      
        customListTile(
          'Password',
          FontAwesomeIcons.lock,
          PasswordPage(),
        ),
        SizedBox(height: 15.h),
        customListTile(
         'Settings',
         FontAwesomeIcons.gear,
          SettingsPage(),

       ),
       
     
     
       SizedBox(height: 15.h),
        customListTile(
          'Delete Account ',
          FontAwesomeIcons.fileLines,
          DeleteAccountPage(),
        ),
        
        SizedBox(height: 15.h),
                  SizedBox(height: 100.h),
                  Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                  padding:  EdgeInsets.only(top: 15.h, bottom: 30.h),
                  child: Obx(() {
                        if (authController.isLoading.value) {
                          return SizedBox(
                            width: 16.w,
                            height: 16.w,
                            child: const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.primary),
                            ),
                          );
                        } else {
                          return CustomButton(
                            text: "Log Out",
                            onPressed: () {
                                authController.logout();
                            },
                          );
                        }
                      }),
                          ),
                        ),
                        
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}



class LanguageSelector extends StatefulWidget {
  @override
  _LanguageSelectorState createState() => _LanguageSelectorState();
}

class _LanguageSelectorState extends State<LanguageSelector> {
  Locale? selectedLocale;
  final AuthController authController = Get.find<AuthController>();

  @override
  void initState() {
    super.initState();
    _loadLocale();
  }

  Future<void> _loadLocale() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? languageCode = prefs.getString('language_code');
    String? countryCode = prefs.getString('country_code');

    if (languageCode == null || countryCode == null) {
      Locale deviceLocale = PlatformDispatcher.instance.locale;
      languageCode = deviceLocale.languageCode;
      countryCode = deviceLocale.countryCode ?? 'GB';
      await _saveLocale(Locale(languageCode, countryCode));
    }

    setState(() {
      selectedLocale = Locale(languageCode ?? 'en', countryCode ?? 'GB');
    });

    Get.updateLocale(selectedLocale!);
  }

  Future<void> _saveLocale(Locale locale) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('language_code', locale.languageCode);
    await prefs.setString('country_code', locale.countryCode ?? 'GB');
  }

  void _changeLanguage(Locale locale) {
    setState(() {
      selectedLocale = locale;
      _saveLocale(selectedLocale!);
      Get.updateLocale(selectedLocale!);
      authController.changeLanguage(locale.languageCode);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('select_language'.tr),
      ),
      body: selectedLocale == null
          ? Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  _buildLanguageButton('🇲🇷 '+"arabic".tr, Locale('ar', 'SA')),
                  SizedBox(height: 16),
                  _buildLanguageButton('🇫🇷 '+"french".tr, Locale('fr', 'FR')),
                  SizedBox(height: 16),
                  _buildLanguageButton('🇬🇧 '+"english".tr, Locale('en', 'GB')),
                ],
              ),
            ),
    );
  }

  Widget _buildLanguageButton(String title, Locale locale) {
    bool isSelected = selectedLocale == locale;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _changeLanguage(locale),
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16),
          backgroundColor: isSelected ? Colors.grey[200] : AppColors.surface,
          foregroundColor:  Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          side: isSelected
              ? BorderSide(color: AppColors.primary, width: 2)
              : BorderSide(color: Colors.grey[200]!),
        ),
        child: Text(
          title,
          style: TextStyle(fontSize: 18),
        ),
      ),
    );
  }
}

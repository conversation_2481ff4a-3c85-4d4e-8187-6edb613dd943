import 'dart:io';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/utils/color_utils.dart';
import 'package:boutigak/utils/deepLinkHandler.dart';
import 'package:boutigak/views/boutigat_store/orderdeatails_page.dart';
import 'package:boutigak/views/boutigat_store/store_widgets.dart' as store_widgets;
import 'package:boutigak/views/widgets/snapchat_item_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

class StoreDetailsPage extends StatefulWidget {
  final Store store;
  final int? selectedItemId;

  const StoreDetailsPage({
    Key? key,
    required this.store,
    this.selectedItemId,
  }) : super(key: key);

  @override
  _StoreDetailsPageState createState() => _StoreDetailsPageState();
}

class _StoreDetailsPageState extends State<StoreDetailsPage> {
  final StoreController storeController = Get.put(StoreController());
  final double expandedHeight = 300.0;
  Color? dominantColor = AppColors.surface;
  Color? surfaceColor = AppColors.surface;
  String? currentImageUrl;

  late final ItemController itemController;
  late final OrderController orderController;
  late bool isFollowing;
  bool isFollowLoading = false;
  
  // Loading states
  bool isLoadingCategories = true;
  bool isLoadingImage = true;

  @override
  void initState() {
    super.initState();

    // Instanciez vos contrôleurs d'items et d'ordres
    itemController = Get.put(ItemController());
    orderController = Get.put(OrderController(initialOrderId: 1));

    // Définir d'abord le currentStoreId pour éviter un clear() tardif
    storeController.currentStoreId.value = widget.store.id!.toString();

    // 1) CHARGER en parallèle Favoris (catégories) et Items
    Future.wait([
      storeController.fetchStoreFavoriteCategories(widget.store.id!.toString()),
      storeController.fetchStoreItems(widget.store.id!.toString()),
    ]).then((_) {
      // 2) Ensuite, on sélectionne "All"
      storeController.selectAllCategories();
      
      // Update loading states
      setState(() {
        isLoadingCategories = false;
      });
    });

    // Logique existante
    _initializeStore();
    isFollowing = widget.store.isFollowed;
  }

  Future<void> _initializeStore() async {
    if (widget.store.images.isNotEmpty) {
      currentImageUrl = '${widget.store.images.first}';
      await _updateDominantColor(currentImageUrl!);
      setState(() {
        isLoadingImage = false;
      });
    } else {
      setState(() {
        isLoadingImage = false;
      });
    }

    if (widget.selectedItemId != null) {
      print('Selected item ID: ${widget.selectedItemId}');
      await _handleSelectedItem();
    }
  }

  Future<void> _handleSelectedItem() async {
    try {
      final Item? selectedItem = await ItemService.getItemById(widget.selectedItemId!);
      print('selected item ${selectedItem?.toJson()}');
      if (selectedItem != null && mounted) {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => ItemBottomSheetWidget(
            item: selectedItem,
            store: widget.store,
            isFromDeepLink: true,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error handling selected item: $e');
    }
  }
bool isDarkColor(Color color) {
  // Luminance de 0.0 (noir) à 1.0 (blanc)
  final luminance = color.computeLuminance();
  return luminance < 0.5; // < 0.5 = sombre, ≥ 0.5 = claire
}

  Future<void> _updateDominantColor(String imageUrl) async {
  final color = await getMostFrequentColor(imageUrl);
  setState(() {
    dominantColor = color;
    surfaceColor = isDarkColor(color) ? Colors.white : Colors.black;
  });
}

  Widget _buildShimmerItems() {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;
    
    return CustomScrollView(
      slivers: <Widget>[
        SliverToBoxAdapter(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 120,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: EdgeInsets.only(left: sidePadding, right: sidePadding, bottom: 20),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 344 / 517,
              crossAxisSpacing: sidePadding,
              mainAxisSpacing: 10,
            ),
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                return Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: screenWidth * 0.4388,
                        height: screenWidth * 0.5485,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      SizedBox(height: 8),
                      Container(
                        width: screenWidth * 0.3,
                        height: 14,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      SizedBox(height: 4),
                      Align(
                        alignment: Alignment.centerRight,
                        child: Container(
                          width: screenWidth * 0.2,
                          height: 12,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
              childCount: 8, // Show 8 shimmer placeholders
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerCategories() {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: 6, // Show 6 shimmer placeholders
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8 ,vertical: 8),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: 80,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _toggleFollow() async {
    setState(() {
      isFollowLoading = true;
    });

    try {
      Map<String, dynamic>? result = await StoreService.followUnfollowStore(
        widget.store.id!,
        isFollowing,
      );


        print('result ${result}');

      if (result != null && result['success'] == true) {


        setState(() {
          isFollowing = result['is_followed'] ?? !isFollowing;
          widget.store.isFollowed = isFollowing;

          // Update followers count from API response
          widget.store.followersCount = result['followers_count'] ?? widget.store.followersCount;
        });
      }
    } catch (e) {
      debugPrint('Error toggling follow: $e');
    } finally {
      setState(() {
        isFollowLoading = false;
      });
    }
  }

 @override
  Widget build(BuildContext context) {
    final store = widget.store;

    return Scaffold(
      body: Stack(
        children: [
        
          if (store.images.isNotEmpty) ...[
            NestedScrollView(
              headerSliverBuilder:
                  (BuildContext context, bool innerBoxIsScrolled) {
                return <Widget>[
                  SliverAppBar(
  expandedHeight: expandedHeight,
  pinned: true,
  floating: true,
  elevation: 0,
  automaticallyImplyLeading: false, // ✅ Supprime la flèche automatique
  backgroundColor: dominantColor ?? AppColors.primary,

  // ✅ Bouton retour custom
  leading: Padding(
  padding: const EdgeInsets.all( 8.0),
  child: Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: () => Navigator.of(context).pop(),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        height: 36, // ✅ Taille visible
        width: 36,
        decoration: BoxDecoration(
          color: Colors.white,
         
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Icon(
            Icons.arrow_back,
            size: 20, // ✅ Icône plus petite
            color: Colors.black,
          ),
        ),
      ),
    ),
  ),
),


  flexibleSpace: FlexibleSpaceBar(
    collapseMode: CollapseMode.pin,
    background: Stack(
      fit: StackFit.expand,
      children: [
        isLoadingImage
            ? Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(color: Colors.grey[300]),
              )
            : Image.network(
                '${store.images.first}',
                fit: BoxFit.cover,
              ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 50,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.1),
                ],
              ),
            ),
          ),
        ),
      ],
    ),
  ),
                  actions: [
  // Bouton Follow (blanc avec bordure noire épaisse)
  Padding(
    padding: const EdgeInsets.only(right: 8.0),
    child: TextButton(
      onPressed: isFollowLoading ? null : _toggleFollow,
      style: TextButton.styleFrom(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),

        ),
        foregroundColor: AppColors.onSurface,
      ),
      child: isFollowLoading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.onSurface),
              ),
            )
          : Text(isFollowing ? "Following" : "Follow"),
    ),
  ),

  // Bouton Followers (noir avec bordure blanche épaisse)
  TextButton(
    onPressed: () {},
    style: TextButton.styleFrom(
      backgroundColor: Colors.black,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
       
      ),
      foregroundColor: AppColors.surface,
    ),
    child: Text("${store.followersCount} Followers"),
  ),

  // Menu burger (fond blanc avec bordure noire épaisse)
  Container(
    height: 40,
    width: 40,
    margin: const EdgeInsets.only(left: 8, right: 8),
    decoration: BoxDecoration(
      color: Colors.white,
    
      borderRadius: BorderRadius.circular(8),
    ),
    child: IconButton(
      icon: const Icon(Icons.menu, size: 20),
      onPressed: () {
        showGeneralDialog(
          context: context,
          barrierLabel: "StoreMenu",
          barrierDismissible: true,
          barrierColor: Colors.black.withOpacity(0.3),
          transitionDuration: const Duration(milliseconds: 300),
          pageBuilder: (context, anim1, anim2) {
            return Align(
              alignment: Alignment.centerRight,
              child: StoreSideMenu(store: widget.store),
            );
          },
          transitionBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1, 0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeOut,
              )),
              child: child,
            );
          },
        );
      },
    ),
  ),
],

                  ),
                  SliverPersistentHeader(
                    delegate: _SliverAppBarDelegate(
                      child: FavoriteCategoryListWidget(
                        storeId: store.id!,
                        isLoading: isLoadingCategories,
                        shimmerBuilder: _buildShimmerCategories,
                      ),
                      height: 62,
                    ),
                    pinned: true,
                  ),
                ];
              },
              body: Container(
                color: Color(0xFFf2f3f5),
                child: Column(
                  children: [
                    Expanded(
                      child: Obx(() {
                        final selectedCat = storeController.selectedCategory.value;
                        if (selectedCat == null) {
                          // "All" => Afficher tous les items
                          return store_widgets.StoreitemsListViewWidget(
                            category: null,
                            items: storeController.myItems,
                            storeImage:
                                '${store.images.first}',storeId: store.id.toString(),
                          );
                        } else {
                          // Filtrer par la catégorie
                          final filteredItems = storeController.myItems
                              .where(
                                (item) =>
                                    item.categoryId == selectedCat.id,
                              )
                              .toList();

                          return store_widgets.StoreitemsListViewWidget(
                            category: selectedCat,
                            items: filteredItems,
                            storeId: store.id.toString(),
                            storeImage:
                                '${store.images.first}',
                          );
                        }
                      }),
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            
            Scaffold(
              body: Column(
                children: [
                  // Shimmer for header
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      height: expandedHeight,
                      color: Colors.grey[300],
                    ),
                  ),
                  // Shimmer for categories
                  Container(
                    height: 20,
                    color: Theme.of(context).colorScheme.surface,
                    child: _buildShimmerCategories(),
                  ),
                  // Shimmer for content
                  Expanded(
                    child: _buildShimmerItems(),
                  ),
                ],
              ),
            ),
          ],

        
          Obx(() {
        
            if (orderController.items.isNotEmpty) {
              return Positioned(
                bottom: 20,
                right: 20,
                child: FloatingActionButton(
                  onPressed: () {

                    orderController.calculateTotal();

                    Get.to(() => CartPage(
                          orderController: orderController,
                          store: widget.store,
                          dominentColor: dominantColor ?? Colors.black,
                          surfaceColor: surfaceColor ?? AppColors.surface
                        ));
                  },
                  backgroundColor: dominantColor,
                  child: Icon(Icons.shopping_cart, color: surfaceColor),
                ),
              );
            } else {
              return SizedBox.shrink();
            }
          }),
        ],
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  _SliverAppBarDelegate({
    required this.child,
    required this.height,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => height;
  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return oldDelegate.child != child || oldDelegate.height != height;
  }
}

// ---------------------------------------------------------------------
// ------------------   FavoriteCategoryListWidget   --------------------
// ---------------------------------------------------------------------
class FavoriteCategoryListWidget extends StatefulWidget {
  final int storeId;
  final bool isLoading;
  final Widget Function()? shimmerBuilder;

  const FavoriteCategoryListWidget({
    Key? key,
    required this.storeId,
    this.isLoading = false,
    this.shimmerBuilder,
  }) : super(key: key);

  @override
  _FavoriteCategoryListWidgetState createState() => _FavoriteCategoryListWidgetState();
}

class _FavoriteCategoryListWidgetState extends State<FavoriteCategoryListWidget> {
  final StoreController storeController = Get.find<StoreController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      color: Theme.of(context).colorScheme.surface,
      child: widget.isLoading 
        ? (widget.shimmerBuilder?.call() ?? _buildDefaultShimmer())
        : Obx(() {
            // 1) On lit les catégories favorites
            final favorites = storeController.FavoriteCategories;

            // 2) On lit la catégorie sélectionnée
            final selectedCat = storeController.selectedCategory.value;

            // 3) Nombre total : +1 pour inclure "All"
            final itemCount = favorites.length + 1;

            return ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: itemCount,
              itemBuilder: (context, index) {
                if (index == 0) {
                  // "All"
                  final isSelected = (selectedCat == null);
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: TextButton(
                      onPressed: () {
                        storeController.selectAllCategories();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.onSurface,
                      ),
                      child: Text(
                        "All",
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          color: 
                               Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                  );
                } else {
                  // Une catégorie favorite réelle
                  final category = favorites[index - 1];
                  final isSelected = (selectedCat == category);

                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: TextButton(
                      onPressed: () {
                        storeController.selectCategory(category, widget.storeId.toString());
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.onSurface,
                      ),
                      child: Text(
                        category.getTitle(),
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          color:
                              Theme.of(context).colorScheme.onSurface

                        ),
                      ),
                    ),
                  );
                }
              },
            );
          }),
    );
  }

  Widget _buildDefaultShimmer() {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: 6, // Show 6 shimmer placeholders
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: 80,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        );
      },
    );
  }
}


class StoreMapDialog extends StatelessWidget {
  final double latitude;
  final double longitude;
  final String storeName;

  const StoreMapDialog({
    Key? key,
    required this.latitude,
    required this.longitude,
    required this.storeName,
  }) : super(key: key);

  Future<void> _openGoogleMaps() async {
    final url = Uri.parse('https://www.google.com/maps/search/?api=1&query=$latitude,$longitude');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _openWaze() async {
    final url = Uri.parse('https://waze.com/ul?ll=$latitude,$longitude&navigate=yes');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _openAppleMaps() async {
    final url = Uri.parse('http://maps.apple.com/?daddr=$latitude,$longitude');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  Widget _buildMapOption({
    required IconData icon,
    required String label,
   
    required VoidCallback onTap,
  }) {
    return Container(
      height: 45.h,
      margin: EdgeInsets.symmetric(vertical: 6.h),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8.r),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Row(
              children: [
                FaIcon(icon, size: 20.sp, color: Colors.black,),
                SizedBox(width: 12.w),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              storeName,
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20.h),

            // Google Maps button
            _buildMapOption(
              icon: FontAwesomeIcons.mapLocationDot,
              label: 'Google Maps',
        
              onTap: _openGoogleMaps,
            ),

            // Waze button
            _buildMapOption(
              icon: FontAwesomeIcons.waze,
              label: 'Waze',
            
              onTap: _openWaze,
            ),

            // Apple Maps button (only on iOS)
            if (Platform.isIOS)
              _buildMapOption(
                icon: Icons.map,
                label: 'Apple Plans',
                
                onTap: _openAppleMaps,
              ),

       
         
          ],
        ),
      ),
    );
  }
}

class StoreSideMenu extends StatelessWidget {
  final Store store;

  const StoreSideMenu({super.key, required this.store});

Widget buildStoreSocialLinks(Store store) {
  final List<Map<String, dynamic>> socials = [
    {
      'icon': FontAwesomeIcons.whatsapp,
      'url': 'https://wa.me/221234567890',
      'color': Color(0xFF25D366), // Vert WhatsApp
    },
    {
      'icon': FontAwesomeIcons.facebookF,
        'url': 'https://www.facebook.com/TonNomDePage',
      'color': Color(0xFF1877F2), // Bleu Facebook
    },
    {
      'icon': 'snapchat',
       'url': 'https://www.snapchat.com/add/tonpseudo',
      'color': Color(0xFFFFFC00), // Jaune Snapchat
    },
    {
      'icon': FontAwesomeIcons.tiktok,
      'url': 'https://www.tiktok.com/@tonpseudo',
      'color': Color(0xFF000000), // Noir TikTok
    },
  ];

   return Padding(
    padding: EdgeInsets.only(top: 16.h,left: 16.w,right: 16.w),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: socials.map((data) {
        final hasUrl = data['url'] != null;
        return Expanded(
          child: hasUrl
              ? Container(
                  height: 50.w,
                
                  margin: EdgeInsets.symmetric(horizontal: 4.w),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(8.r),
                      onTap: () async {
                        final uri = Uri.parse(data['url']);
                        if (await canLaunchUrl(uri)) {
                          await launchUrl(uri, mode: LaunchMode.externalApplication);
                        } else {
                          Get.snackbar('Erreur', 'Impossible d’ouvrir le lien');
                        }
                      },
                      child: Center(
                        child: data['icon'] == 'snapchat'
                            ? Image.asset(
                                 'assets/images/snapchat.png',
                                width: 22.w,
                                height: 22.w,
                              )
                            : FaIcon(
                                data['icon'],
                                size: 20.sp,
                                color: data['color'],
                              ),
                      ),
                    ),
                  ),
                )
              : const SizedBox.shrink(),
        );
      }).toList(),
    ),
  );
}

 


  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.8,
      padding:  EdgeInsets.only(left: 16.w ,top: 60.h),
      decoration: BoxDecoration(
        color: Colors.white, // adapt to theme if needed
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  store.images.isNotEmpty ? store.images.first : '',
                  height: 60.w,
                  width: 60.w,
                  fit: BoxFit.cover,
                ),
              ),
               SizedBox(width: 12.w),
           SizedBox(height: 12.h),
               Row(
  mainAxisAlignment: MainAxisAlignment.spaceBetween,
  children: [
    // Nom du store
    Container(
      constraints: const BoxConstraints(minWidth: 125, maxWidth: 125),
      child: Text(
        store.name,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.bold,
          decoration: TextDecoration.none,
          color: AppColors.onSurface,
        ),
      ),
    ),

    // Icône Partage (style carré)
    Container(
      height: 40.w,
      width: 40.w,
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8.r),
          onTap: () {
            Navigator.pop(context);
            showDialog(
              context: context,
              builder: (_) => FractionallySizedBox(
                heightFactor: 0.6,
                child: CapturableStoreWidget(
                  storeId: store.id.toString(),
                  imageUrl: store.images.first,
                  storeType: store.typeName ?? '',
                  storeName: store.name,
                ),
              ),
            );
          },
          child: Center(
            child: Icon(
              Icons.share,
                 color: AppColors.onSurface, // Orange
              size: 22.sp,
            ),
          ),
        ),
      ),
    ),

    // Icône Localisation (style carré)
    Container(
      height: 40.w,
      width: 40.w,
      margin: EdgeInsets.symmetric(horizontal: 3.w),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8.r),
          onTap: () {
            Navigator.pop(context);
            showDialog(
              context: context,
              builder: (_) => FractionallySizedBox(
                heightFactor: 0.6,
                child: StoreMapDialog(
                  latitude: store.latitude ?? 18.0735,
                  longitude: store.longitude ?? -15.9582,
                  storeName: store.name,
                ),
              ),
            );
          },
          child: Center(
            child: Icon(
              Icons.location_on,
              color: AppColors.onSurface, // Rouge
              size: 22.sp,
            ),
          ),
        ),
      ),
    ),
  ],
),

            ],
          ),
        SizedBox(height: 16.h),
        Text(
         
         "Description".tr,
        
          style:  TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            decoration: TextDecoration.none,
            color: AppColors.onSurface,
          ),
        ),
          SizedBox(height: 16.h),
          if (store.description != null)
       SizedBox(
  height: 400.h, // ≈5 lignes selon le style
  child: Scrollbar(
    thumbVisibility: true,
    child: SingleChildScrollView(
      child: Text(
        store.description,
        style: TextStyle(fontSize: 14, color: Colors.grey[700],decoration: TextDecoration.none,fontWeight: AppFontWeights.medium,),
      ),
    ),
  ),
),
SizedBox(height: 32.h),
     buildStoreSocialLinks(store),

        ],
      ),
    );
  }
}

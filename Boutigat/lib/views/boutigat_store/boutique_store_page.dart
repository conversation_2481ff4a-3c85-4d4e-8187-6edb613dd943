import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:shimmer/shimmer.dart';
import 'boutiques_store_widgets.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';





class StorePage extends StatefulWidget {
  const StorePage({Key? key}) : super(key: key);

  @override
  State<StorePage> createState() => _StorePageState();
}

class _StorePageState extends State<StorePage> {
  final TextEditingController searchController = TextEditingController();
  final StoreController storeController = Get.put(StoreController());
  final AuthController authController = Get.find<AuthController>();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
 final FocusNode focusNode = FocusNode();
  @override
  void initState() {
    super.initState();
    // Initial load if stores are empty
    if (storeController.recommendedStores.isEmpty) {
      storeController.fetchRecomandedStores(refresh: true);
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String value) {
    if (value.isEmpty) {
      storeController.clearSearch();
    } else {
      storeController.searchStores(value);
    }
  }

  Future<void> _onRefresh() async {
    await storeController.fetchRecomandedStores(refresh: true);
    _refreshController.refreshCompleted();
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: PreferredSize(
  preferredSize: const Size.fromHeight(kToolbarHeight + 16),
  child: SafeArea(
    bottom: false, // on protège surtout le haut
    child: Container(
      padding: const EdgeInsets.only(left: 18, top: 4 ,bottom: 4),
      color: Theme.of(context).colorScheme.surface,
      child: Row(
  children: [
    Text(
      'stores'.tr,
      style: TextStyle(
        color: Theme.of(context).colorScheme.primary,
        fontSize: AppTextSizes.heading,
        fontWeight: AppFontWeights.bold,
      ),
    ),
    const SizedBox(width: 12),
    Expanded(
      child: CupertinoTextField(
        controller: searchController,
        focusNode: focusNode,
        placeholder: "search".tr,
        prefix: IconButton(
          icon: Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
          onPressed: () {
            _onSearchChanged(searchController.text);
          },
        ),
        suffix: IconButton(
          icon: Icon(Icons.clear, color: Theme.of(context).colorScheme.onSurface),
          onPressed: () {
            searchController.clear();
            _onSearchChanged('');
            focusNode.unfocus();
          },
        ),
        onChanged: _onSearchChanged,
        onSubmitted: (value) {
          _onSearchChanged(value);
          focusNode.unfocus();
        },
      ),
    ),
   
    GestureDetector(
      onTap: () {
  
          ZoomDrawer.of(context)!.toggle();
        
      },
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * 0.04,
        ),
        child: authController.isAuthenticated.value
            ? CircleAvatar(
                radius: 22,
                backgroundColor: Colors.grey[300],
                child: Text(
                  '${authController.user?.firstName[0] ?? ''}${authController.user?.lastName[0] ?? ''}'.toUpperCase(),
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            : Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.onSurface,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  FontAwesomeIcons.solidUser,
                  size: 15,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
      ),
    ),
  ],
),


    ),
  ),
),

      body: Obx(() {
       if (storeController.isLoading.value && storeController.recommendedStores.isEmpty) {
  return ListView.builder(
    padding: const EdgeInsets.symmetric(vertical: 8),
    itemCount: 5,
    itemBuilder: (context, index) => const BoutiqueShimmerWidget(),
  );
}


        if (storeController.recommendedStores.isEmpty && !storeController.isLoading.value) {
          return const Center(child: Text("Aucune boutique disponible."));
        }

       return SmartRefresher(
  controller: _refreshController,
  enablePullDown: true,
  header: CustomHeader(
    height: 100.h,
    builder: (context, mode) {
      if (mode == RefreshStatus.refreshing) {
        return Lottie.asset(
          'assets/lottie/loader.json',
          width: 75.w,
          height: 75.w,
        );
      } else if (mode == RefreshStatus.completed) {
        return Lottie.asset(
          'assets/lottie/Done.json',
          width: 65.w,
          height: 65.w,
          repeat: false,
        );
      } else if (mode == RefreshStatus.failed) {
        return const Icon(Icons.error, color: Colors.red);
      } else {
        return const Icon(Icons.arrow_downward);
      }
    },
  ),
  onRefresh: _onRefresh,
  child: ListView.builder(
    controller: storeController.storeScrollController,
    padding: const EdgeInsets.symmetric(vertical: 8),
    itemCount: storeController.recommendedStores.length +
        (storeController.isLoadingMoreStores.value ? 1 : 0),
    itemBuilder: (context, index) {
      if (index == storeController.recommendedStores.length) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      final boutique = storeController.recommendedStores[index];
      return BoutiqueWidget(
        boutique: boutique,
        fontSize: 18,
        iconSize: 12,
      );
    },
  ),
);

      }),
    );
  }
}

class BoutiqueShimmerWidget extends StatelessWidget {
  const BoutiqueShimmerWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Image shimmer
            Container(
              height: 160,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(15),
              ),
            ),
            const SizedBox(height: 10),

            // Titre + type boutique (nom + tag à droite)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: 14,
                  width: 120,
                  color: Colors.white,
                ),
                Container(
                  height: 12,
                  width: 60,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Ligne de statut fermé simulé
           Row(
  mainAxisSize: MainAxisSize.min,
  children: [
    Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 5),
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(5),
        ),
        child: Container(
          width: 100, // ✅ largeur identique à ton widget réel
          height: 12,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    ),
  ],
),

          ],
        ),
      ),
    );
  }
}

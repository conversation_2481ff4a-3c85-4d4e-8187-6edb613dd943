import 'dart:convert';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/data/models/messages.dart';
class ConversationService {



  // create discussion

   static Future<dynamic> createDiscussion(int itemId) async {
    try {
      final response = await WebService.post(
        AvailableServices.discussionsItem,
        body: {
          'item_id': itemId,
        },
      );


      log('create discussion response: ${response.body}');

      if (response.statusCode == 201 || response.statusCode == 200) {
     //   // Get.snakbar("Success", "Discussion created successfully");
        return jsonDecode(response.body);
      } else {
     //   // Get.snakbar("Error", "Failed to create discussion. Status code: ${response.statusCode}");
        return null;
      }
    } catch (e) {
     // // Get.snakbar("Error", "An error occurred while creating the discussion: ${e.toString()}");
      print("Error creating discussion: $e");
      return false;
    }
  }


  static Future<bool> createOffer(int itemId, double price) async {
    try {
    //  print("in service itemId ${itemId} price $price");

      final response = await WebService.post(
        AvailableServices.offers,
        body: {
          'item_id': itemId,
          'price': price,
        },
      );

    //  print("Create offer response status: ${response.statusCode}");
    //  print("Create offer response body: ${response.body}");

      if (response.statusCode == 201) {
       // // Get.snakbar("Success", "Offer created successfully");
        return true;
      } else {
        final errorBody = jsonDecode(response.body);
     //   // Get.snakbar("Error", "Failed to create offer: ${errorBody['error']}");
        return false;
      }
    } catch (e) {
    //  // Get.snakbar("Error", "An error occurred while creating the offer: ${e.toString()}");
      print("Error creating offer: $e");
      return false;
    }
  }

  static Future<Map<String, dynamic>?> getDiscussions({int page = 1, int perPage = 9}) async {
    try {
      final response = await WebService.get(
        '${AvailableServices.discussions}',
        params: {
          'page': page.toString(),
          'per_page': perPage.toString(),
        },
      );

      log('discu ${response.body}');
      log('response status code ${response.statusCode}');

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = jsonDecode(response.body);

        // Return the full response with data and pagination info
        return responseData;
      } else {
        // Get.snackbar("Error", "Failed to load discussions");
        return null;
      }
    } catch (e) {
      // Get.snackbar("Error", "An error occurred while fetching discussions: ${e.toString()}");
      log("Error fetching discussions: $e");
      return null;
    }
  }

  static Future<bool> sendMessage(int discussionID, String? content , bool isOffer , double? price , bool isStoreDiscussion  ) async {

    try {
      final response = await WebService.post(
        '${AvailableServices.discussionsItem}/$discussionID/messages',
        body: ({
          'content': content ?? '',
          'is_an_offer': isOffer,
          'price' : price ?? 0,
          'is_store_discussion' : isStoreDiscussion
        }),
      );

    log("Send message response status: ${response.statusCode}");
   log("Send message response body: ${response.body}");

      if (response.statusCode == 201) {
     //   // Get.snakbar("Success", "Message sent");
        return true;
      } else {
        final errorBody = jsonDecode(response.body);
      //  // Get.snakbar("Error", "Failed to send message: ${errorBody['message']}");
        return false;
      }
    } catch (e) {
   //   // Get.snakbar("Error", "An error occurred while sending the message: ${e.toString()}");
      print("Error sending message: $e");
      return false;
    }
  }

static Future<List<Message>> getDiscussionDetails(int discussionId) async {
  try {
    final response = await WebService.get('${AvailableServices.discussionsItem}/$discussionId');
    print("Response status: ${response.statusCode}");
    log("Messages body: ${response.body}");

    if (response.statusCode == 200) {
      dynamic body = jsonDecode(response.body);
      List<Message> messages = [];

      var discussion = body;
      var messagesByDate = discussion['messages_by_date'] as Map<String, dynamic>;

      messagesByDate.forEach((dateCategory, messagesList) {
        messages.addAll((messagesList as List).map((messageJson) => Message.fromJson(messageJson, dateCategory)).toList());
      });

      // Sort messages by timestamp in ascending order (oldest first)
      messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      return messages;
    } else {
      return [];
    }
  } catch (e) {
    print("An error occurred while fetching discussion details: $e");
    return [];
  }
}


}
